{"name": "eastclassifiedbackend", "version": "1.0.0", "main": "index.js", "type": "module", "scripts": {"start": "node app.js", "start:dev": "nodemon app.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@prisma/client": "^6.15.0", "bcryptjs": "^3.0.2", "body-parser": "^2.2.0", "cors": "^2.8.5", "express": "^5.1.0", "jsonwebtoken": "^9.0.2", "nodemailer": "^7.0.6", "nodemon": "^3.1.10"}, "devDependencies": {"prisma": "^6.15.0"}}