generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}


model Users {
  id        Int      @id @default(autoincrement())
  name      String
  email     String   @unique
  password  String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  loginType String @default("email")
  socialAuthId String @default("")
  isVerified Boolean @default(false)
}

model categories {
  id            Int             @id @default(autoincrement())
  name          String
  createdAt     DateTime        @default(now())
  updatedAt     DateTime        @updatedAt
  subCategories subCategories[]
}

model subCategories {
  id         Int         @id @default(autoincrement())
  name       String
  createdAt  DateTime    @default(now())
  updatedAt  DateTime    @updatedAt
  category   categories  @relation(fields: [categoryId], references: [id])
  categoryId Int
}


