require("dotenv").config();
const express = require("express");
const body_parser = require("body-parser");
const cors = require("cors");
const authroutes = require("./Routes/Auth/AuthRoutes.js");
const categoryroutes = require("./Routes/CategoryRoutes/CategoryRoutes.js");

const app = express();
const isProduction = process.env.NODE_ENV === "production";
const clientOrigin = isProduction
  ? process.env.PROD_CLIENT_URL
  : process.env.DEV_CLIENT_URL;

// middlewares
app.use(express.json());
app.use(express.urlencoded({ extended: true }));
app.use(body_parser.json());
// Cors
app.use(
  cors({
    origin: clientOrigin,
    credentials: true,
  })
);

app.use("/user", authroutes);
app.use("/api", categoryroutes);

app.get("/", (req, res) => {
  res.send("Hello World!");
});

const port = process.env.PORT || 8000;

app.listen(port, () => {
  console.log(`Server is running on port http://localhost:${port}`);
});
