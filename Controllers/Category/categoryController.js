const { PrismaClient } = require("@prisma/client");
const prisma = new PrismaClient();

async function handleCreateCategory(req, res) {
  try {
    const { name, subcategory } = req.body;
    if (!name) {
      return res.status(400).json({
        success: false,
        message: "Name of the category is required",
      });
    }
    const category = await prisma.categories.create({
      data: {
        name,
        subCategories: {
          create:
            subcategory && subcategory.length > 0
              ? { create: subcategory.map((sub) => ({ name: sub })) }
              : undefined,
        },
      },
      include: { subCategories: true },
    });
    res.status(201).json({
      success: true,
      message: "Category created successfully",
      data: category,
    });
  } catch (error) {
    console.error("Error creating category:", error);
    res.status(500).json({
      success: false,
      message: "Internal server error",
      error: error.message,
    });
  }
}

// Get all categories
async function handleGetAllCategories(res) {
  try {
    const categories = await prisma.categories.findMany({
      include: { subCategories: true },
    });
    res.status(200).json({
      success: true,
      message: "Categories fetched successfully",
      data: categories,
    });
  } catch (error) {
    console.error("Error fetching categories:", error);
    res.status(500).json({
      success: false,
      message: "Internal server error",
      error: error.message,
    });
  }
}

module.exports = { handleCreateCategory, handleGetAllCategories };
