const { PrismaClient } = require("@prisma/client");
const JwtUtil = require("../../Utils/Jwt/jwtUtil.js");
const { transporter } = require("../../Utils/Email/EmailUtil.js");

const fs = require("fs");
const path = require("path");
const prisma = new PrismaClient();

const templatePath = path.join(__dirname, "../../Templates/otp.html");
let otpTemplate = fs.readFileSync(templatePath, "utf8");

// Login Type is equal to email
async function registerWithEmail(email, hash, loginType, name) {
  try {
    const user = await prisma.users.create({
      data: {
        email,
        password: hash,
        loginType,
        name,
      },
    });
    return user;
  } catch (error) {
    console.log(error);
    return null;
  }
}

async function handleRegisterUser(req, res) {
  try {
    const { email, password, loginType, name } = req.body;
    console.log(req.body);

    if (!email || !password || !loginType || !name) {
      return res.status(400).json({
        message: "All fields are required",
      });
    }

    const userExists = await prisma.users.findUnique({
      where: {
        email,
      },
    });

    if (userExists) {
      return res.status(400).json({
        message: "User already exists",
      });
    }

    await JwtUtil.hashPassword(password)
      .then(async (hash) => {
        if (loginType === "email") {
          const user = await registerWithEmail(email, hash, loginType, name);
          return res.status(200).json({
            message: "User created successfully",
            user,
          });
        } else {
          return res.status(400).json({
            message: "Invalid login type",
          });
        }
      })
      .catch((error) => {
        console.log(error);
        return res.status(500).json({
          message: "Something went wrong",
          error,
        });
      });
  } catch (error) {
    console.log(error);
    return res.status(500).json({
      message: "Something went wrong",
    });
  }
}

// handle send otp via email
async function handleSendOtp(req, res) {
  try {
    const { email } = req.body;
    if (!email) {
      return res.status(400).json({
        message: "Email is required",
      });
    }

    const user = await prisma.users.findUnique({
      where: {
        email,
      },
    });

    if (!user) {
      return res.status(400).json({
        message: "User does not exist",
      });
    }

    const otp = Math.floor(100000 + Math.random() * 900000).toString();
    const html = otpTemplate.replace("{{OTP_CODE}}", otp);
    await transporter.sendMail({
      from: "East Classified",
      to: email,
      subject: "OTP for East Classified",
      html,
    });
    return res.status(200).json({
      message: "OTP sent successfully",
      otp: otp,
    });
  } catch (error) {
    console.log(error);
    return res.status(500).json({
      message: "Internal server error",
    });
  }
}

// handle verify otp
async function handleVerifyOtp(req, res) {
  try {
    const { email, otp, userotp } = req.body;
    console.log(req.body);
    if (!email || !otp) {
      return res.status(400).json({
        message: "Email and OTP are required",
      });
    }

    const user = await prisma.users.findUnique({
      where: {
        email,
      },
    });
    if (!user) {
      return res.status(400).json({
        message: "User does not exist",
      });
    }

    if (otp !== userotp) {
      console.log(otp, userotp);
      return res.status(400).json({
        message: "Invalid OTP",
      });
    }

    await prisma.users
      .update({
        where: {
          email,
        },
        data: {
          isVerified: true,
        },
      })
      .then(async () => {
        const token = JwtUtil.generateToken(user);
        return res.status(200).json({
          message: "OTP verified successfully",
          token,
          user,
        });
      })
      .catch((error) => {
        console.log(error);
        return res.status(500).json({
          message: "Internal server error",
        });
      });
  } catch (error) {
    console.log(error);
    return res.status(500).json({
      message: "Internal server error",
    });
  }
}

// handle login
async function handleLoginUser(req, res) {
  try {
    const { email, password } = req.body;

    if (!email || !password) {
      return res.status(400).json({
        message: "All fields are required",
      });
    }

    const user = await prisma.users.findUnique({
      where: {
        email,
      },
    });

    if (!user) {
      return res.status(400).json({
        message: "User does not exist",
      });
    }

    const isPasswordValid = await JwtUtil.comparePassword(
      password,
      user.password
    );

    if (!isPasswordValid) {
      return res.status(400).json({
        message: "Invalid credentials",
      });
    }

    const token = JwtUtil.generateToken(user);
    return res.status(200).json({
      message: "Login successful",
      token,
      user,
    });
  } catch (error) {
    console.log(error);
    return res.status(500).json({
      message: "Something went wrong",
    });
  }
}

module.exports = {
  handleRegisterUser,
  handleLoginUser,
  handleSendOtp,
  handleVerifyOtp,
};
